:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --light-color: #ecf0f1;
    --dark-color: #2c3e50;
    --background-color: #f5f5f5;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
    --border-radius: 8px;
    --content-width: 1200px;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Roboto', Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: var(--background-color);
    line-height: 1.6;
    color: #333;
}

header {
    background-color: var(--primary-color);
    color: var(--light-color);
    padding: 15px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: var(--box-shadow);
    position: sticky;
    top: 0;
    z-index: 1000;
}

header img {
    height: 50px;
    margin: 0;
    max-width: 100%;
    object-fit: contain;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

header img:hover {
    transform: scale(1.05);
}

header h1 {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
    color: var(--light-color);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    letter-spacing: 0.5px;
    position: relative;
    padding-bottom: 2px;
    transition: var(--transition);
}

header h1::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--secondary-color);
    transition: width 0.3s ease;
}

header:hover h1::after {
    width: 100%;
}

nav {
    background-color: var(--primary-color);
    color: var(--light-color);
    padding: 10px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

nav a {
    color: var(--light-color);
    margin: 0 15px;
    text-decoration: none;
    font-weight: 500;
    position: relative;
    transition: var(--transition);
    padding: 5px 0;
}

nav a:hover {
    color: var(--secondary-color);
}

nav a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 0;
    background-color: var(--secondary-color);
    transition: var(--transition);
}

nav a:hover::after {
    width: 100%;
}

.container {
    max-width: var(--content-width);
    margin: 0 auto;
    padding: 20px;
    padding-bottom: 60px;
}

.content {
    background-color: #ffffff;
    padding: 25px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 20px;
    transition: var(--transition);
}

.content:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

h2 {
    color: var(--primary-color);
    margin-bottom: 15px;
    position: relative;
    padding-bottom: 10px;
    font-weight: 600;
    display: inline-block;
}

h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background-color: var(--secondary-color);
    transition: width 0.3s ease;
}

.content:hover h2::after {
    width: 100%;
}

p {
    margin-bottom: 15px;
}

iframe {
    width: 100%;
    height: 400px;
    border: 0;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

footer {
    background-color: var(--primary-color);
    color: var(--light-color);
    text-align: center;
    padding: 20px 0;
    margin-top: 20px;
}

.contact {
    background-color: var(--light-color);
    padding: 25px;
    border-radius: var(--border-radius);
    margin-top: 20px;
    box-shadow: var(--box-shadow);
}

.contact h2 {
    color: var(--primary-color);
}

.contact p {
    color: var(--dark-color);
    margin-bottom: 10px;
}

.contact h3 {
    color: var(--primary-color);
    margin: 20px 0 10px 0;
    font-size: 1.2rem;
    border-bottom: 1px solid var(--secondary-color);
    padding-bottom: 5px;
    display: inline-block;
}

.contact strong {
    color: var(--primary-color);
}

.contact em {
    color: var(--secondary-color);
}

.contact a {
    color: var(--secondary-color);
    text-decoration: none;
    transition: var(--transition);
}

.contact a:hover {
    text-decoration: underline;
    color: var(--primary-color);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    z-index: 1001;
}

.bar {
    width: 25px;
    height: 3px;
    background-color: var(--light-color);
    margin: 4px 0;
    transition: var(--transition);
}

.pricing-table {
    overflow-x: auto;
    margin: 20px 0;
}

.pricing-table table {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
    box-shadow: var(--box-shadow);
    border-radius: var(--border-radius);
}

.pricing-table th,
.pricing-table td {
    padding: 15px;
    text-align: center;
    border: 1px solid #e0e0e0;
}

.pricing-table th {
    background-color: var(--primary-color);
    color: var(--light-color);
    font-weight: 500;
}

.pricing-table td:first-child {
    text-align: left;
    background-color: var(--light-color);
}

.pricing-table tr:nth-child(even) {
    background-color: #f8f9fa;
}

.pricing-table tr:hover {
    background-color: #f1f3f5;
}

#events {
    margin-top: 20px;
}

/* Animace */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.content {
    animation: fadeIn 0.5s ease-out;
}

.content:nth-child(2) {
    animation-delay: 0.2s;
}

.content:nth-child(3) {
    animation-delay: 0.4s;
}

/* Media Queries */
@media (max-width: 768px) {
    nav {
        display: none;
        flex-direction: column;
        width: 100%;
        position: absolute;
        top: 70px;
        left: 0;
        background-color: var(--primary-color);
        padding: 20px 0;
        box-shadow: var(--box-shadow);
        z-index: 999;
    }

    nav a {
        margin: 10px 0;
        font-size: 1.1rem;
    }

    .hamburger {
        display: flex;
    }

    .content {
        padding: 20px;
    }

    h2 {
        font-size: 1.5rem;
    }

    .pricing-table {
        margin: 10px -15px;
    }

    .pricing-table th,
    .pricing-table td {
        padding: 10px;
        font-size: 0.9rem;
    }
}

@media (max-width: 600px) {
    header h1 {
        font-size: 1.6rem;
        max-width: 250px;
    }
}

@media (max-width: 480px) {
    header {
        flex-direction: row;
        justify-content: space-between;
        padding: 10px 15px;
    }

    header h1 {
        font-size: 1.4rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 200px;
    }

    header img {
        height: 40px;
        margin-right: 10px;
    }

    .container {
        padding: 10px;
        padding-bottom: 40px;
    }

    .content {
        padding: 15px;
    }

    iframe {
        height: 300px;
    }

    h2 {
        font-size: 1.3rem;
    }
}

@media (min-width: 769px) {
    nav {
        display: flex;
        justify-content: center;
    }
}
